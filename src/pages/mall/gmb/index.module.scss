.page {
  overflow: hidden;
  line-height: 1.3;
}

.head {
  margin: 45px 35px 0;
  height: 420px;
  border-radius: 30px 30px 0 0;
  // background: linear-gradient(104.2531deg, #d5b57c 0%, #e6d2b0 100%);
  box-sizing: border-box;
  padding: 50px 70px 0;
  background: url(https://oss.gaomei168.com/file-release/20241109/1304742369934716928.png)
    no-repeat center;
  background-size: cover;

  .tit {
    font-size: 37px;
    color: #fff;
  }

  .num {
    margin: 40px 0;
    font-size: 113px;
    color: #fff;
    font-weight: bold;
  }

  .desc {
    display: flex;
    align-items: center;
    font-size: 37px;
    color: #fff;
  }

  .sm {
    font-size: 32px;
    margin: 0 50px;
  }
}

.filter {
  height: 104px;
  background: #eee;
  padding: 0 50px;
  display: flex;
  align-items: center;

  .date {
    display: flex;
    align-items: center;
    font-size: 37px;
    color: #000;
  }

  .arr {
    margin: 0 30px;
    width: 25px;
    height: 25px;
    background: url(../../../assets/arr-d.png) no-repeat center/contain;
  }

  .extra {
    margin-left: auto;
    font-size: 33px;
    color: #666;
  }
}

.list {
  background: #fff;
  .item {
    height: 246px;
    padding: 0 70px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
  }

  .left {
    .name {
      font-size: 43px;
      color: #000;
      margin-bottom: 25px;
    }
    .date {
      font-size: 31px;
      color: #666;
    }
  }

  .right {
    margin-left: auto;
    font-size: 55px;
  }

  .em {
    color: #ad7f6d;
  }
}

.empty {
  padding: 50px;
  font-size: 30px;
  color: #aaa;
  text-align: center;
}
