import { Image, Swiper, SwiperItem, View } from "@tarojs/components";
import Style from "./index.module.scss";
import { useMount, useSetState } from "ahooks";
import clsx from "clsx";
import Taro, { useRouter } from "@tarojs/taro";
import CusApi from "@/services/CusApi";
import RichContent from "@/components/RichContent";
import { openConcat, str2arr } from "@/utils/tools";
import GoodsShare from "@/components/GoodsShare";
import CusNav from "./CusNav";
import { UnoIcon } from "@/components/UnoIcon";
import Comments from "./Comments";
import { ExContent } from "./ExContent";
import { useShare } from "@/stores/useShare";
import { usePageStat } from "@/stores/usePageStat";
import { SkuComp } from "./SkuComp/SkuComp";

export default function Info() {
  const router = useRouter();
  const [state, setState] = useSetState({
    id: router.params.id || "",
    open: false,
    swiperIdx: 0,

    data: {} as any,

    share: false,

    sku: null as any,
  });

  usePageStat({
    id: state.id,
    title: state.data?.name,
  });

  useShare(() => {
    return {
      title: state.data.name,
      imageUrl: state.data.coverImage,
      path: `/pages/mall2/info/index?id=${router.params.id}`,
    };
  });

  useMount(() => {
    fetchData();
  });

  const fetchData = async () => {
    const res = await CusApi.getGoodsInfo({ id: router.params.id });

    res._bannerImages = str2arr(res.bannerImages);
    res._showImages = str2arr(res.showImages);
    res._labels = str2arr(res.labelName);
    res._parts = str2arr(res.partName).join("、");
    // console.log(res);
    // res.priceSensitiveTag = 1;

    setState({ data: res });
  };

  return (
    <View>
      <CusNav />
      <View className={Style.page}>
        <View className={Style.banner}>
          <Swiper
            className={Style.swiper}
            current={state.swiperIdx}
            onChange={(e) => setState({ swiperIdx: e.detail.current })}
            indicatorDots={false}
            indicatorColor="rgba(255,255,255,.3)"
            indicatorActiveColor="#fff"
          >
            {state.data._bannerImages?.map((item, idx) => (
              <SwiperItem
                className={Style.item}
                key={idx}
                onClick={() => {
                  Taro.previewImage({
                    current: item,
                    urls: state.data._bannerImages,
                  });
                }}
              >
                <Image
                  mode="aspectFill"
                  className={Style.img}
                  src={item}
                ></Image>
              </SwiperItem>
            ))}
          </Swiper>

          {state.data._bannerImages?.length > 1 && (
            <View className="dots pos-absolute left-0 bottom-15 w-full flex items-center justify-center gap-6">
              {state.data._bannerImages.map((item, idx) => (
                <View
                  key={item.id}
                  className={clsx(
                    "size-10 bg-#000/30 rounded-full",
                    state.swiperIdx == idx && "uno-layer-z1-(bg-#AE7F6D)"
                  )}
                />
              ))}
            </View>
          )}
        </View>

        <View className={Style.pane}>
          {/* <View className={Style.tit}>{state.data.name}</View> */}
          <View className="flex items-center pt-40 pb-20">
            <View className="min-w-0 text-(40 #111)">{state.data.name}</View>
            {!!state.data._labels?.length && (
              <View className="flex-shrink-0 flex gap-8 flex-wrap ml-20">
                {state.data._labels?.map((n, i) => {
                  const colors = ["#985e4a", "#a38958"];
                  const bgs = ["#f7f4ef", "#eee7df"];

                  const c = colors[i % colors.length];
                  const b = bgs[i % bgs.length];

                  return (
                    <View
                      key={n}
                      className="h-30 text-(22/30 #985e4a) bg-#f7f4ef px-8 rounded-4 overflow-hidden"
                      style={{ backgroundColor: b, color: c }}
                    >
                      {n}
                    </View>
                  );
                })}
              </View>
            )}
          </View>
          {!!state.data._parts?.length && (
            <View className="text-(22 #888) pb-15 break-all">
              推荐{state.data._parts}等部位
            </View>
          )}
          <View className={Style.info}>
            <View className={Style.left}>
              <View className={Style.price}>
                <View className={Style.num}>
                  &yen;
                  {state.data.priceSensitiveTag ? "???" : state.data.salePrice}
                </View>
                {state.data?.productSpecificationNum > 1 && (
                  <View className="text-(24 #333) ml-10">起</View>
                )}
              </View>
              {!state.data.priceSensitiveTag && (
                <View className={Style.old}>
                  原价：&yen;{state.data.oldPrice}
                </View>
              )}
            </View>
            {state.data.inventoryLimit == 1 && (
              <View className={Style.stock}>
                库存剩余:{state.data.inventoryCount}
              </View>
            )}
          </View>
          <View className={Style.list}>
            {state.data.productModel === 2 &&
              state.data.includeProductList?.map((item) => (
                <View className={Style.item} key={item.id}>
                  <View className={Style.dot}>·</View>
                  <View className={Style.name}>{item.name}</View>
                  {/* <View className={Style.price2}>&yen;{item.oldPrice}</View> */}
                  {/* <View className={Style.price1}>&yen;{item.salePrice}</View> */}
                </View>
              ))}
          </View>
          <View className={Style.foot}>
            {"正品保证 医生主导 无隐形消费 有边界感服务".split(" ").map((n) => (
              <View className={Style.tag} key={n}>
                <Image
                  className={Style.ico}
                  mode="scaleToFill"
                  src="https://vip.gaomei168.com/file-release/file/100/2024/0408/10/1226838040437956608_size_1387.png"
                ></Image>
                <View className={Style.txt}>{n}</View>
              </View>
            ))}
          </View>
        </View>

        <View
          className="mt-30 rounded-20 px-30 py-20 bg-#fff overflow-hidden b-(1 solid #8E4E36) flex items-center"
          onClick={() => setState({ open: true })}
        >
          <View className="text-(28 #11100F) fw-bold mr-20">项目</View>
          <View className="text-(24 #11100F/80)">
            {state.sku ? state.sku?.name : "请选择规格"}
          </View>
          <View className="text-(20 #999) ml-a">点击查看&nbsp;&gt;</View>
        </View>

        <View
          className="px-0 mt-20"
          onClick={() => Taro.navigateTo({ url: `/pages/mall2/brand/index` })}
        >
          <Image
            className="block w-full h-a"
            mode="widthFix"
            src="https://oss.gaomei168.com/file-release/20250312/1349421215172165632_750_166.png"
          />
        </View>

        {/* <ShopBox data={state} onChange={(e) => setState(e)}></ShopBox> */}

        <Comments pid={router.params.id || ""}></Comments>

        <View className={Style.mtitle}>-项目详情-</View>

        <View className="bg-#fff p-20 -mx-20">
          <RichContent html={state.data.content}></RichContent>
          <ExContent></ExContent>
        </View>

        <View
          className={Style.fixbtn}
          onClick={() => setState({ share: true })}
        >
          <UnoIcon className="i-icon-park-outline:share size-30 text-#fff" />
          <View className={Style.txt}>分享</View>
        </View>

        <View className={Style.footerHolder}></View>
        <View className={Style.footer}>
          <View
            className={Style.menu}
            onClick={() => Taro.reLaunch({ url: "/pages/tabs/mall/index" })}
          >
            <Image
              className={Style.ico}
              mode="aspectFill"
              src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241019/1297147475487625216.png"
            ></Image>
            <View className={Style.txt}>首页</View>
          </View>
          <View className={Style.menu} onClick={openConcat}>
            <Image
              className={Style.ico}
              mode="scaleToFill"
              src="https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20241019/1297147475462459392.png"
            ></Image>
            <View className={Style.txt}>咨询</View>
          </View>
          <View className={Style.btn} onClick={() => setState({ open: true })}>
            <View className={Style.item}>加入购物车</View>
            <View className={Style.item}>立即购买</View>
          </View>
        </View>

        {!!state.data?.id && (
          <SkuComp
            open={state.open}
            onClose={() => setState({ open: false })}
            data={state.data}
            onChange={(e) => setState({ sku: e })}
          />
        )}

        {!!state.data?.id && (
          <GoodsShare
            open={state.share}
            data={state.data}
            onOK={() => setState({ share: false })}
          />
        )}
      </View>
    </View>
  );
}
