import { Image, ScrollView, Text, View } from "@tarojs/components";
import { SkuList } from "./SkuList";
import { PopUp } from "@/components/Custom/PopUp2";
import { UnoIcon } from "@/components/UnoIcon";
import { Counter } from "./Counter";
import { useSetState } from "ahooks";
import { ModeList } from "./ModeList";
import { showToast } from "@/utils/tools";
import Taro from "@tarojs/taro";
import CusApi from "@/services/CusApi";
import { ShopList } from "./ShopList";
import { useEffect } from "react";
import { Attribute } from "./SkuList";

export const SkuComp = (props: {
  data?: any;
  open?: boolean;
  onClose?: any;
  onChange?: any;
}) => {
  const data = props.data;
  const [state, setState] = useSetState({
    num: 1,
    mode: null as any,
    sku: null as any,
    shop: null as any,
    doctor: null as any,
    missingAttrs: [] as Attribute[],
  });

  useEffect(() => {
    props.onChange?.(state.sku);
  }, [state.sku]);

  // 生成未选择规格的提示文本
  const getUnselectedSpecsTip = () => {
    if (!state.missingAttrs?.length) return "请选择规格";

    // 只取第一个未选中的规格名称
    return `请选择${state.missingAttrs[0].name}`;
  };

  const toCart = async () => {
    if (!state.sku) return showToast(getUnselectedSpecsTip());

    Taro.showLoading({ title: "加载中" });
    await CusApi.addCart({
      style: 1,
      productId: props.data?.id,
      cartCount: state.num,
      shopId: state.shop?.id || "",
      serviceUserId: state.doctor?.id || "",
      model: state.mode,
      productSpecificationId: state.sku?.id,
    });
    Taro.showToast({ title: "加入购物车成功", icon: "success" });
    props.onClose?.();
  };

  const toBuy = async () => {
    if (!state.sku) return showToast(getUnselectedSpecsTip());

    const data = props.data || {};
    const shop = state.shop || {};
    const doctor = state.doctor || {};
    const sku = state.sku || {};

    const group = {
      id: "",
      model: state.mode,
      // productModel: data.productModel,
      shopId: shop.id,
      shopName: shop.name,
      productList: [
        {
          id: "",
          cartCount: state.num,
          cartPrice: sku?.salePrice,
          productCoverImage: data.coverImage,
          productId: data.id,
          model: state.mode,
          // productModel: data.productModel,
          productName: data.name,
          selectState: 1,
          serviceFee: doctor.fee,
          serviceUserId: doctor.id,
          serviceUserName: doctor.name,
          shopId: shop.id,
          shopName: shop.name,
          productSpecificationId: sku.id,
          productSpecificationName: sku.name,
          // outpatientFee: shop?.outpatientFee,
          // updateDate: 1714980650000,
        },
      ],
    };

    props.onClose?.();
    Taro.preload(group);
    Taro.navigateTo({ url: "/pages/mall2/order/index" });
  };

  return (
    <PopUp open={props.open} onClose={props.onClose}>
      <View className="bg-#fff box-border">
        <View className="flex gap-20 pos-relative px-30 pt-30 bg-#fff pb-20">
          <Image
            className="size-190 bg-#f5f5f5"
            mode="aspectFill"
            src={data.coverImage}
          />
          <View className="flex-1 min-w-0 flex flex-col py-4">
            <View className="text-(34 #8D451F) fw-bold first-letter:(text-25 mr-5)">
              &yen;
              {data?.priceSensitiveTag
                ? "???"
                : state.sku
                ? state.sku.salePrice
                : data.salePrice}
            </View>

            <View className="flex items-center mt-10">
              <View className="min-w-0 text-(28 #333) fw-bold line-clamp-1">
                {state.sku ? (
                  state.sku?.name
                ) : (
                  <Text className="text-#666">{getUnselectedSpecsTip()}</Text>
                )}
              </View>
              {!!data._labels?.length && (
                <View className="flex-shrink-0 flex gap-8 flex-wrap ml-20">
                  {data._labels?.map((n, i) => {
                    const colors = ["#985e4a", "#a38958"];
                    const bgs = ["#f7f4ef", "#eee7df"];

                    const c = colors[i % colors.length];
                    const b = bgs[i % bgs.length];

                    return (
                      <View
                        key={n}
                        className="h-30 text-(22/30 #985e4a) bg-#f7f4ef px-8 rounded-4 overflow-hidden"
                        style={{ backgroundColor: b, color: c }}
                      >
                        {n}
                      </View>
                    );
                  })}
                </View>
              )}
            </View>

            {!!data._parts?.length && (
              <View className="text-(22 #888) mt-10 break-all">
                推荐{data._parts}等部位
              </View>
            )}

            <Counter
              value={state.num}
              onChange={(newNum) => setState({ num: newNum })}
              min={1}
            />
          </View>
          <View onClick={props.onClose} className="absolute top-30 right-30">
            <UnoIcon className="i-icon-park-outline:close size-34 text-#666" />
          </View>
        </View>

        <ScrollView scrollY className="max-h-50vh">
          <View className="px-30 pb-80">
            <ModeList data={data} onChange={(e) => setState({ mode: e })} />
            <SkuList
              data={data}
              onChange={(sku, missingAttrs) =>
                setState({ sku, missingAttrs: missingAttrs || [] })
              }
            />
            <ShopList
              hideUI
              data={data}
              mode={state.mode}
              sku={state.sku}
              onChange={(shop, doctor) => setState({ shop, doctor })}
            />
          </View>
        </ScrollView>

        <View className="p-30 !pb-0">
          <View className="flex h-80 b-(1 solid #8E4E36) rounded-full overflow-hidden">
            <View
              className="flex-1 flex items-center justify-center text-(30 #8E4E36) bg-#fff"
              onClick={toCart}
            >
              加入购物车
            </View>
            <View
              className="flex-1 flex items-center justify-center text-(30 #fff) bg-#8E4E36"
              onClick={toBuy}
            >
              立即购买
            </View>
          </View>
          <View
            style={{
              height: `env(safe-area-inset-bottom)`,
              minHeight: "30rpx",
            }}
          />
        </View>
      </View>
    </PopUp>
  );
};
